# A2A Integration in McpPreviewEditor

This document describes the A2A (Agent-to-Agent) integration added to the McpPreviewEditor.

## Overview

The McpPreviewEditor now supports both MCP (Model Context Protocol) and A2A protocols, allowing users to:

1. Switch between MCP and A2A protocols using a toggle button
2. View and interact with A2A agents
3. Send messages to A2A agents and receive responses
4. Test individual agents

## Features

### Protocol Switching

- A toggle button in the header allows switching between "MCP" and "A2A" modes
- The UI dynamically updates to show the appropriate content for each protocol
- Search functionality works for both protocols

### A2A Agent Display

- Agents are displayed as cards showing:
  - Agent name and version
  - Description
  - Provider information
  - Number of skills (if any)
  - Test and Details links

### Agent Interaction

- Users can send test messages to agents
- Responses are displayed in the result panel
- Error handling for connection issues

## Components

### A2AAgentCardPanel

Displays individual agent information in a card format, similar to MCP tool cards.

**Features:**
- Agent name, version, and description
- Provider information
- Skills count
- Test and Details buttons

### A2AAgentListPanel

Manages and displays multiple agent cards from configured A2A servers.

**Features:**
- Loading agents from multiple servers
- Filtering agents by search text
- Error handling for server connection issues
- Automatic fallback to default servers if none configured

### A2A Configuration Integration

A2A servers are now configured through the existing MCP configuration system.

**Features:**
- A2A servers are defined in the same configuration file as MCP servers
- Uses the `a2aServer` field in McpConfig
- Leverages CustomMcpServerManager for configuration parsing

## Configuration

### MCP Configuration File

A2A servers are configured in the same JSON file as MCP servers. The configuration structure is:

```json
{
  "mcpServers": {
    "server1": {
      "command": "mcp-server-command",
      "args": ["--arg1", "--arg2"]
    }
  },
  "a2aServer": {
    "server1": {
      "url": "http://localhost:8080"
    },
    "server2": {
      "url": "http://localhost:3000"
    }
  }
}
```

### Configuration Loading

The system uses the existing MCP configuration infrastructure:
- Configuration is loaded via `McpServer.load(content)`
- A2A servers are extracted from the `a2aServer` field
- Uses `CustomMcpServerManager.instance(project)` for configuration management

## Usage

1. Open an MCP configuration file in the editor
2. The McpPreviewEditor will show with MCP content by default
3. Click "Switch to A2A" button to view A2A agents
4. Use the search field to filter agents
5. Click "Test" on any agent card to send a test message
6. Click "Details" to view full agent information

## Technical Details

### Protocol Detection

The editor maintains a `currentProtocol` state that determines:
- Which content panel to display (MCP tools vs A2A agents)
- Which search functionality to use
- Which chat functionality to use

### Message Handling

- MCP messages use the existing MCP chat infrastructure
- A2A messages use the A2AClientConsumer for communication
- Both protocols display results in the same result panel

### Error Handling

- Connection errors are displayed in the agent list
- Message sending errors are shown in notifications
- Graceful fallback to default configurations

## Future Enhancements

1. **Agent Selection**: Allow users to select which agent to send messages to
2. **Streaming Responses**: Support for streaming A2A responses
3. **Configuration UI**: Add a settings dialog for managing A2A servers
4. **Authentication**: Support for authenticated A2A connections
5. **Batch Operations**: Support for sending messages to multiple agents

## Dependencies

- `io.a2a.client.A2AClient`: A2A client library
- `io.a2a.spec.*`: A2A specification classes
- Existing MCP infrastructure for UI components
