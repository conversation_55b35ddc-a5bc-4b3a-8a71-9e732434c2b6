package cc.unitmesh.devti.a2a.ui

import cc.unitmesh.devti.a2a.A2AClientConsumer
import cc.unitmesh.devti.a2a.A2aServer
import cc.unitmesh.devti.a2a.A2AServerManager
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import io.a2a.spec.AgentCard
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.FlowLayout
import java.awt.GridBagConstraints
import java.awt.GridBagLayout
import javax.swing.*

class A2AAgentListPanel(
    private val project: Project
) : JPanel(BorderLayout()) {
    private val a2aClientConsumer = A2AClientConsumer()
    private val a2aServerManager = A2AServerManager.getInstance(project)
    private val textGray = JBColor(0x6B7280, 0x9DA0A8)

    private fun getAgentName(agent: AgentCard): String = try {
        val nameField = agent.javaClass.getDeclaredField("name")
        nameField.isAccessible = true
        nameField.get(agent) as? String ?: ""
    } catch (e: Exception) {
        ""
    }

    private fun getAgentDescription(agent: AgentCard): String? = try {
        val descField = agent.javaClass.getDeclaredField("description")
        descField.isAccessible = true
        descField.get(agent) as? String
    } catch (e: Exception) {
        null
    }

    private fun getProviderName(agent: AgentCard): String? = try {
        val providerField = agent.javaClass.getDeclaredField("provider")
        providerField.isAccessible = true
        val provider = providerField.get(agent)
        if (provider != null) {
            val nameField = provider.javaClass.getDeclaredField("name")
            nameField.isAccessible = true
            nameField.get(provider) as? String
        } else {
            null
        }
    } catch (e: Exception) {
        null
    }
    
    private var loadingJob: Job? = null
    private val serverLoadingStatus = mutableMapOf<String, Boolean>()
    private val serverPanels = mutableMapOf<String, JPanel>()
    private val allA2AAgents = mutableMapOf<String, List<AgentCard>>()
    private val currentFilteredAgents = mutableMapOf<String, List<AgentCard>>()

    init {
        background = UIUtil.getPanelBackground()
        border = JBUI.Borders.empty()
        layout = BorderLayout()
    }

    fun loadAgents(servers: List<A2aServer>? = null, onAgentsLoaded: (MutableMap<String, List<AgentCard>>) -> Unit = {}) {
        val serversToUse = servers ?: getConfiguredServers()
        loadingJob?.cancel()
        serverLoadingStatus.clear()
        serverPanels.clear()
        allA2AAgents.clear()
        currentFilteredAgents.clear()

        SwingUtilities.invokeLater {
            removeAll()
            revalidate()
            repaint()
        }

        if (serversToUse.isEmpty()) {
            SwingUtilities.invokeLater {
                showNoServersMessage()
            }
            return
        }

        loadingJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                // Initialize A2A clients
                a2aClientConsumer.init(serversToUse)

                SwingUtilities.invokeLater {
                    serversToUse.forEach { server ->
                        serverLoadingStatus[server.url] = true
                        createServerSection(server.url)
                    }
                }

                // Load agents from all servers
                val jobs = serversToUse.map { server ->
                    launch {
                        try {
                            val agents = a2aClientConsumer.listAgents()
                            
                            synchronized(allA2AAgents) {
                                allA2AAgents[server.url] = agents
                                currentFilteredAgents[server.url] = agents
                            }
                            
                            SwingUtilities.invokeLater {
                                updateServerSection(server.url, agents)
                                serverLoadingStatus[server.url] = false
                            }
                        } catch (e: Exception) {
                            SwingUtilities.invokeLater {
                                showServerError(server.url, e.message ?: "Unknown error")
                                serverLoadingStatus[server.url] = false
                            }
                        }
                    }
                }
                
                jobs.forEach { it.join() }
                onAgentsLoaded(allA2AAgents)
            } catch (e: Exception) {
                SwingUtilities.invokeLater {
                    showGeneralError(e.message ?: "Failed to load A2A agents")
                }
            }
        }
    }

    fun filterAgents(searchText: String) {
        if (searchText.isEmpty()) {
            currentFilteredAgents.clear()
            currentFilteredAgents.putAll(allA2AAgents)
        } else {
            currentFilteredAgents.clear()
            allA2AAgents.forEach { (serverUrl, agents) ->
                val filtered = agents.filter { agent ->
                    getAgentName(agent).contains(searchText, ignoreCase = true) ||
                    getAgentDescription(agent)?.contains(searchText, ignoreCase = true) == true ||
                    getProviderName(agent)?.contains(searchText, ignoreCase = true) == true
                }
                if (filtered.isNotEmpty()) {
                    currentFilteredAgents[serverUrl] = filtered
                }
            }
        }

        SwingUtilities.invokeLater {
            updateAllServerSections()
        }
    }

    private fun createServerSection(serverUrl: String) {
        val serverPanel = JPanel(BorderLayout()).apply {
            background = UIUtil.getPanelBackground()
            border = JBUI.Borders.empty(8, 0)
        }

        val headerPanel = JPanel(FlowLayout(FlowLayout.LEFT)).apply {
            background = UIUtil.getPanelBackground()
        }

        val serverLabel = JBLabel("A2A Server: $serverUrl").apply {
            font = JBUI.Fonts.label(13.0f).asBold()
        }

        val loadingLabel = JBLabel("Loading...").apply {
            font = JBUI.Fonts.label(12.0f)
            foreground = textGray
        }

        headerPanel.add(serverLabel)
        headerPanel.add(loadingLabel)

        val agentsPanel = JPanel(GridBagLayout()).apply {
            background = UIUtil.getPanelBackground()
            border = JBUI.Borders.empty(4, 0)
        }

        serverPanel.add(headerPanel, BorderLayout.NORTH)
        serverPanel.add(agentsPanel, BorderLayout.CENTER)

        serverPanels[serverUrl] = agentsPanel

        add(serverPanel)
        revalidate()
        repaint()
    }

    private fun updateServerSection(serverUrl: String, agents: List<AgentCard>) {
        val agentsPanel = serverPanels[serverUrl] ?: return
        agentsPanel.removeAll()

        if (agents.isEmpty()) {
            val noAgentsLabel = JBLabel("No agents available for $serverUrl").apply {
                foreground = textGray
                horizontalAlignment = SwingConstants.LEFT
            }
            agentsPanel.add(noAgentsLabel)
        } else {
            val gbc = GridBagConstraints().apply {
                gridx = 0
                gridy = 0
                anchor = GridBagConstraints.NORTHWEST
                fill = GridBagConstraints.HORIZONTAL
                weightx = 1.0
                insets = JBUI.insets(2)
            }

            agents.forEach { agent ->
                val panel = A2AAgentCardPanel(project, agent, a2aClientConsumer)
                agentsPanel.add(panel, gbc)
                gbc.gridy++
            }

            // Add a filler component to push cards to the top
            gbc.weighty = 1.0
            gbc.fill = GridBagConstraints.BOTH
            agentsPanel.add(JPanel().apply { background = UIUtil.getPanelBackground() }, gbc)
        }

        agentsPanel.revalidate()
        agentsPanel.repaint()
    }

    private fun updateAllServerSections() {
        currentFilteredAgents.forEach { (serverUrl, agents) ->
            updateServerSection(serverUrl, agents)
        }
    }

    private fun showServerError(serverUrl: String, errorMessage: String) {
        val agentsPanel = serverPanels[serverUrl] ?: return
        agentsPanel.removeAll()

        val errorLabel = JBLabel("Error loading agents from $serverUrl: $errorMessage").apply {
            foreground = JBColor.RED
            horizontalAlignment = SwingConstants.LEFT
        }
        agentsPanel.add(errorLabel)
        agentsPanel.revalidate()
        agentsPanel.repaint()
    }

    private fun showNoServersMessage() {
        removeAll()

        val noServersPanel = JPanel(BorderLayout()).apply {
            background = UIUtil.getPanelBackground()
            border = JBUI.Borders.empty(16)
        }

        val noServersLabel = JBLabel("No A2A servers configured. Please check your configuration.").apply {
            foreground = textGray
            horizontalAlignment = SwingConstants.CENTER
        }

        noServersPanel.add(noServersLabel, BorderLayout.CENTER)
        add(noServersPanel)
        revalidate()
        repaint()
    }

    private fun showGeneralError(errorMessage: String) {
        removeAll()

        val errorPanel = JPanel(BorderLayout()).apply {
            background = UIUtil.getPanelBackground()
            border = JBUI.Borders.empty(16)
        }

        val errorLabel = JBLabel("Error: $errorMessage").apply {
            foreground = JBColor.RED
            horizontalAlignment = SwingConstants.CENTER
        }

        errorPanel.add(errorLabel, BorderLayout.CENTER)
        add(errorPanel)
        revalidate()
        repaint()
    }

    fun dispose() {
        loadingJob?.cancel()
    }

    fun getAgents(): Map<String, List<AgentCard>> = allA2AAgents

    fun getA2AClientConsumer(): A2AClientConsumer = a2aClientConsumer

    private fun getConfiguredServers(): List<A2aServer> {
        val configuredServers = a2aServerManager.getServers()
        return if (configuredServers.isEmpty()) {
            A2AServerManager.getDefaultServers()
        } else {
            configuredServers
        }
    }
}
